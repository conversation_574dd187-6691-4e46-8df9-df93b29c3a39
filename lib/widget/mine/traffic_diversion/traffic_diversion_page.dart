import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/mine/traffic_diversion/input_dialog.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class TrafficDiversionPage extends StatefulWidget {
  const TrafficDiversionPage({super.key});

  @override
  State<TrafficDiversionPage> createState() => _TrafficDiversionPageState();
}

class _TrafficDiversionPageState extends State<TrafficDiversionPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  List<AssetEntity> _selectedImages = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '引流名片'),
      backgroundColor: AppTheme.colorPageBacground,
      body: SizedBox(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        child: SingleChildScrollView(
          child: Column(
            children: [
              _videoView(),
              SizedBox(height: 16.h),
              _inputView(),
              SizedBox(height: 16.h),
              _notesView(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _videoView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 16.w),
      decoration: BoxDecoration(
          color: const Color(0xFFEEF4FF),
          borderRadius: BorderRadius.circular(6.r)),
      child: GestureDetector(
        onTap: () {},
        child: Row(
          children: [
            Image.asset('assets/png/mine/tip_left_img.png',
                width: 24, height: 24),
            SizedBox(width: 6.w),
            Expanded(
              child: Text(
                '点击视频教程快速了解推广名片使用场景',
                style: TextStyle(
                  color: AppTheme.colorBlue,
                  fontSize: 12.sp,
                ).pfMedium,
              ),
            ),
            Text(
              '视频教程',
              style: TextStyle(
                      color: AppTheme.colorBlue,
                      fontSize: 12.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: AppTheme.colorBlue)
                  .pfMedium,
            )
          ],
        ),
      ),
    );
  }

  Widget _inputView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          _textFieldView("您的昵称", "如：草草老师、蔡文姬等", _nameController, "请输入昵称"),
          SizedBox(height: 30.h),
          _textFieldView("您的手机号", "学员可以联系到您", _phoneController, "请输入手机号",
              keyboardType: TextInputType.phone),
          SizedBox(height: 30.h),
          _wechatView(),
        ],
      ),
    );
  }

  Widget _textFieldView(
    String title,
    String tipStr,
    TextEditingController controller,
    String hintText, {
    TextInputType? keyboardType,
  }) {
    return GestureDetector(
      onTap: () {
        showDialog(
            context: context,
            builder: (_) {
              return InputDialog(
                  controller: controller,
                  title: title,
                  hintText: hintText,
                  tipStr: tipStr);
            });
      },
      child: SizedBox(
        width: ScreenUtil().screenWidth - 32.w,
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  title,
                  style:
                      TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                          .pfMedium,
                ),
                SizedBox(width: 8.w),
                Text(
                  '($tipStr)',
                  style:
                      TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                          .pfRegular,
                )
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      enabled: false,
                      keyboardType: keyboardType ?? TextInputType.name,
                      controller: controller,
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfMedium,
                      cursorColor: AppTheme.colorBlue,
                      decoration: InputDecoration(
                        hintText: hintText,
                        hintStyle: TextStyle(
                                color: const Color(0xFF606266), fontSize: 14.sp)
                            .pfRegular,
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  Image.asset('assets/png/arrow_right.png',
                      width: 17, height: 17),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _wechatView() {
    return SizedBox(
      width: ScreenUtil().screenWidth - 32.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '您的微信码',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                .pfMedium,
          ),
          SizedBox(height: 8.w),
          Text(
            '(可以上传多个，每次随机推荐给学员，长按可删除)',
            style: TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                .pfRegular,
          ),
          SizedBox(height: 10.w),
          _buildImageGrid(),
        ],
      ),
    );
  }

  // 构建九宫格图片网格
  Widget _buildImageGrid() {
    // 计算网格的列数，最多3列
    int crossAxisCount = 3;
    // 计算每个图片的大小
    double itemSize =
        (ScreenUtil().screenWidth - 32.w - 20.w) / 3; // 减去padding和间距

    // 创建显示项列表（图片 + 添加按钮）
    List<Widget> items = [];

    // 添加已选择的图片
    for (int i = 0; i < _selectedImages.length; i++) {
      items.add(_buildImageItem(_selectedImages[i], i, itemSize));
    }

    // 添加"添加"按钮
    items.add(_buildAddButton(itemSize));

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 10.w,
        mainAxisSpacing: 10.w,
        childAspectRatio: 1.0,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return items[index];
      },
    );
  }

  // 构建单个图片项
  Widget _buildImageItem(AssetEntity asset, int index, double size) {
    return GestureDetector(
      onLongPress: () => _showDeleteDialog(index),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: AssetEntityImage(
            asset,
            fit: BoxFit.cover,
            isOriginal: false,
            thumbnailSize: const ThumbnailSize.square(200),
          ),
        ),
      ),
    );
  }

  // 构建添加按钮
  Widget _buildAddButton(double size) {
    return GestureDetector(
      onTap: _pickImages,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
          color: const Color(0xFFF8F8F8),
        ),
        child: Icon(
          Icons.add,
          size: 30.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  Widget _notesView() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.r), topRight: Radius.circular(12.r))),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      width: ScreenUtil().screenWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '注意事项',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                .pfMedium,
          ),
          SizedBox(height: 21.5.h),
          _noteTextView(
              '1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。'),
          SizedBox(height: 15.h),
          _noteTextView(
              '2.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。'),
          SizedBox(height: 15.h),
          _noteTextView(
              '3.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。'),
          SizedBox(height: 15.h),
          _noteTextView(
              '4.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。'),
        ],
      ),
    );
  }

  Widget _noteTextView(String title) {
    return Text(
      title,
      style:
          TextStyle(color: const Color(0xFF606266), fontSize: 12.sp).pfRegular,
    );
  }

  // 选择图片
  Future<void> _pickImages() async {
    // 检查相册权限
    PermissionState status = await PhotoManager.getPermissionState(
        requestOption: PermissionRequestOption());
    if (status != PermissionState.authorized) {
      status = await PhotoManager.requestPermissionExtend();
      if (status != PermissionState.authorized) {
        return;
      }
    }

    if (!mounted) return;

    try {
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
          maxAssets: 9, // 最多选择9张图片
          selectedAssets: _selectedImages,
          requestType: RequestType.image,
          pathNameBuilder: (AssetPathEntity path) => switch (path) {
            final p when p.isAll => '全部',
            _ => path.name,
          },
        ),
      );

      if (result != null && mounted) {
        setState(() {
          _selectedImages = result;
        });
      }
    } catch (e) {
      // 处理选择图片失败的情况
      debugPrint('选择图片失败: $e');
    }
  }

  // 显示删除确认对话框
  void _showDeleteDialog(int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除图片'),
          content: const Text('确定要删除这张图片吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedImages.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
