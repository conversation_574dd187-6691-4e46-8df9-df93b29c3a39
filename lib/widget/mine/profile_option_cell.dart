import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';

class ProfileOptionCell extends StatelessWidget {
  final String title;
  final String svgAssetPath; // Path to the SVG asset
  final String? trailingText;
  final Color? trailingTextColor;
  final VoidCallback onTap; // Callback for handling tap events

  const ProfileOptionCell({
    required this.title,
    required this.svgAssetPath,
    this.trailingText,
    required this.onTap,
    this.trailingTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60, // Set the fixed height to 60
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          // boxShadow: [
          //   BoxShadow(
          //     color: Colors.grey.withOpacity(0.1),
          //     spreadRadius: 1,
          //     blurRadius: 5,
          //     offset: const Offset(0, 3),
          //   ),
          // ],
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              svgAssetPath,
              width: 24, // Adjust size as needed
              height: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: AppTheme.getTextStyle(
                        baseSize: 15, color: AppTheme.colorPrimaryBlack)
                    .pfMedium,
              ),
            ),
            if (trailingText != null)
              Text(
                trailingText!,
                style: AppTheme.getTextStyle(
                        baseSize: 12,
                        color: trailingTextColor ?? AppTheme.colorLightGrey)
                    .pfRegular,
              ),
            SvgPicture.asset(
              'assets/svg/mine/profile/right_arrow.svg',
              width: 7.47, // Adjust size as needed
              height: 13.58,
            ),
          ],
        ),
      ),
    );
  }
}
