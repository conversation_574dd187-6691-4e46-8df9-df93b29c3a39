import 'dart:io';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/mine/traffic_diversion/input_dialog.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_code_dart_scan/qr_code_dart_scan.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path_provider/path_provider.dart';

class TrafficDiversionPage extends StatefulWidget {
  const TrafficDiversionPage({super.key});

  @override
  State<TrafficDiversionPage> createState() => _TrafficDiversionPageState();
}

class _TrafficDiversionPageState extends State<TrafficDiversionPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  AssetEntity? _selectedImage; // 改为单张图片
  String? _qrCodeData; // 识别出的二维码数据
  List<String> _noticeItems = []; // 注意事项列表

  @override
  void initState() {
    super.initState();
    _loadNoticeConfig();
  }

  // 加载注意事项配置
  Future<void> _loadNoticeConfig() async {
    try {
      // 这里模拟从服务端下载Lua文件并解析
      // 实际项目中应该从服务端下载Lua文件
      await _downloadAndParseLuaConfig();
    } catch (e) {
      debugPrint('加载注意事项配置失败: $e');
      // 使用默认配置
      _setDefaultNoticeItems();
    }
  }

  // 下载并解析Lua配置文件
  Future<void> _downloadAndParseLuaConfig() async {
    // TODO: 实际项目中这里应该从服务端下载Lua文件
    // 这里先使用模拟数据
    await Future.delayed(const Duration(milliseconds: 500));

    // 模拟Lua文件内容解析
    _noticeItems = [
      '1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。',
      '3.系统会自动为您的二维码添加微信图标，提升识别度。',
      '4.建议定期更新您的联系方式，确保学员能够及时联系到您。',
    ];

    if (mounted) {
      setState(() {});
    }
  }

  // 设置默认注意事项
  void _setDefaultNoticeItems() {
    _noticeItems = [
      '1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '2.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '3.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '4.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
    ];
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '引流名片'),
      backgroundColor: AppTheme.colorPageBacground,
      body: SizedBox(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        child: SingleChildScrollView(
          child: Column(
            children: [
              _videoView(),
              SizedBox(height: 16.h),
              _inputView(),
              SizedBox(height: 16.h),
              _notesView(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _videoView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 16.w),
      decoration: BoxDecoration(
          color: const Color(0xFFEEF4FF),
          borderRadius: BorderRadius.circular(6.r)),
      child: GestureDetector(
        onTap: () {},
        child: Row(
          children: [
            Image.asset('assets/png/mine/tip_left_img.png',
                width: 24, height: 24),
            SizedBox(width: 6.w),
            Expanded(
              child: Text(
                '点击视频教程快速了解推广名片使用场景',
                style: TextStyle(
                  color: AppTheme.colorBlue,
                  fontSize: 12.sp,
                ).pfMedium,
              ),
            ),
            Text(
              '视频教程',
              style: TextStyle(
                      color: AppTheme.colorBlue,
                      fontSize: 12.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: AppTheme.colorBlue)
                  .pfMedium,
            )
          ],
        ),
      ),
    );
  }

  Widget _inputView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          _textFieldView("您的昵称", "如：草草老师、蔡文姬等", _nameController, "请输入昵称"),
          SizedBox(height: 30.h),
          _textFieldView("您的手机号", "学员可以联系到您", _phoneController, "请输入手机号",
              keyboardType: TextInputType.phone),
          SizedBox(height: 30.h),
          _wechatView(),
        ],
      ),
    );
  }

  Widget _textFieldView(
    String title,
    String tipStr,
    TextEditingController controller,
    String hintText, {
    TextInputType? keyboardType,
  }) {
    return GestureDetector(
      onTap: () {
        showDialog(
            context: context,
            builder: (_) {
              return InputDialog(
                  controller: controller,
                  title: title,
                  hintText: hintText,
                  tipStr: tipStr);
            });
      },
      child: SizedBox(
        width: ScreenUtil().screenWidth - 32.w,
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  title,
                  style:
                      TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                          .pfMedium,
                ),
                SizedBox(width: 8.w),
                Text(
                  '($tipStr)',
                  style:
                      TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                          .pfRegular,
                )
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      enabled: false,
                      keyboardType: keyboardType ?? TextInputType.name,
                      controller: controller,
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfMedium,
                      cursorColor: AppTheme.colorBlue,
                      decoration: InputDecoration(
                        hintText: hintText,
                        hintStyle: TextStyle(
                                color: const Color(0xFF606266), fontSize: 14.sp)
                            .pfRegular,
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  Image.asset('assets/png/arrow_right.png',
                      width: 17, height: 17),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _wechatView() {
    return SizedBox(
      width: ScreenUtil().screenWidth - 32.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '您的微信码',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                .pfMedium,
          ),
          SizedBox(height: 8.w),
          Text(
            '(可以上传多个，每次随机推荐给学员，长按可删除)',
            style: TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                .pfRegular,
          ),
          SizedBox(height: 10.w),
          _buildImageGrid(),
        ],
      ),
    );
  }

  // 构建图片和二维码显示区域
  Widget _buildImageGrid() {
    return Column(
      children: [
        // 如果有选中的图片，显示生成的二维码
        if (_qrCodeData != null) _buildGeneratedQRCode(),

        SizedBox(height: 10.h),

        // 图片选择区域
        Row(
          children: [
            // 显示选中的图片或添加按钮
            _selectedImage != null
                ? _buildSelectedImageItem()
                : _buildAddButton(),
          ],
        ),
      ],
    );
  }

  // 构建生成的二维码
  Widget _buildGeneratedQRCode() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
      ),
      child: Column(
        children: [
          Text(
            '生成的微信二维码',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 14.sp)
                .pfMedium,
          ),
          SizedBox(height: 10.h),
          Stack(
            alignment: Alignment.center,
            children: [
              // 二维码
              QrImageView(
                data: _qrCodeData!,
                size: 150.w,
                backgroundColor: Colors.white,
              ),
              // 中心的微信图标
              Container(
                width: 30.w,
                height: 30.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Image.asset(
                  'assets/png/im/wechat_icon.png', // 需要添加微信图标
                  width: 24.w,
                  height: 24.w,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.wechat,
                      size: 24.w,
                      color: const Color(0xFF07C160),
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建选中的图片项
  Widget _buildSelectedImageItem() {
    double size = 100.w;
    return GestureDetector(
      onLongPress: () => _showDeleteDialog(),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: AssetEntityImage(
            _selectedImage!,
            fit: BoxFit.cover,
            isOriginal: false,
            thumbnailSize: const ThumbnailSize.square(200),
          ),
        ),
      ),
    );
  }

  // 构建添加按钮
  Widget _buildAddButton() {
    double size = 100.w;
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: const Color(0xFFE5E5E5), width: 1),
          color: const Color(0xFFF8F8F8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              size: 30.sp,
              color: const Color(0xFF999999),
            ),
            SizedBox(height: 4.h),
            Text(
              '选择图片',
              style: TextStyle(
                color: const Color(0xFF999999),
                fontSize: 12.sp,
              ).pfRegular,
            ),
          ],
        ),
      ),
    );
  }

  Widget _notesView() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.r), topRight: Radius.circular(12.r))),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      width: ScreenUtil().screenWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '注意事项',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                .pfMedium,
          ),
          SizedBox(height: 21.5.h),
          // 动态显示注意事项列表
          ..._noticeItems.asMap().entries.map((entry) {
            int index = entry.key;
            String item = entry.value;
            return Column(
              children: [
                _noteTextView(item),
                if (index < _noticeItems.length - 1) SizedBox(height: 15.h),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _noteTextView(String title) {
    return Text(
      title,
      style:
          TextStyle(color: const Color(0xFF606266), fontSize: 12.sp).pfRegular,
    );
  }

  // 选择单张图片
  Future<void> _pickImage() async {
    // 检查相册权限
    PermissionState status = await PhotoManager.getPermissionState(
        requestOption: PermissionRequestOption());
    if (status != PermissionState.authorized) {
      status = await PhotoManager.requestPermissionExtend();
      if (status != PermissionState.authorized) {
        return;
      }
    }

    if (!mounted) return;

    try {
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
          maxAssets: 1, // 只选择一张图片
          selectedAssets: _selectedImage != null ? [_selectedImage!] : [],
          requestType: RequestType.image,
          pathNameBuilder: (AssetPathEntity path) => switch (path) {
            final p when p.isAll => '全部',
            _ => path.name,
          },
        ),
      );

      if (result != null && result.isNotEmpty && mounted) {
        EasyLoading.show(status: '识别二维码中...');

        // 识别二维码
        await _recognizeQRCode(result.first);
      }
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('选择图片失败: $e');
    }
  }

  // 识别二维码并生成新的二维码
  Future<void> _recognizeQRCode(AssetEntity asset) async {
    try {
      File? file = await asset.file;
      if (file?.path != null) {
        final decoder = QRCodeDartScanDecoder(formats: [BarcodeFormat.qrCode]);
        Result? result = await decoder.decodeFile(XFile(file!.path));

        if (result?.text != null) {
          setState(() {
            _selectedImage = asset;
            _qrCodeData = result!.text;
          });
          EasyLoading.showSuccess('二维码识别成功');
        } else {
          EasyLoading.showError('未识别到二维码，请选择包含二维码的图片');
        }
      } else {
        EasyLoading.showError('图片文件读取失败');
      }
    } catch (e) {
      EasyLoading.showError('二维码识别失败: $e');
      debugPrint('二维码识别失败: $e');
    } finally {
      EasyLoading.dismiss();
    }
  }

  // 显示删除确认对话框
  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除图片'),
          content: const Text('确定要删除这张图片和生成的二维码吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedImage = null;
                  _qrCodeData = null;
                });
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
