-- 引流名片注意事项配置文件
-- 此文件应该部署在服务端，Flutter应用会定期下载并解析

-- 配置版本信息
config_info = {
    version = "1.2.0",
    last_updated = "2024-01-15",
    author = "系统管理员"
}

-- 注意事项列表
-- 支持动态修改，修改后客户端会自动更新
notice_items = {
    "1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。",
    "2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。建议使用高清图片，避免模糊或变形。",
    "3.系统会自动为您的二维码添加微信图标，提升识别度和用户体验。",
    "4.建议定期更新您的联系方式，确保学员能够及时联系到您。过期的联系方式可能影响业务转化。",
    "5.如遇到技术问题或需要帮助，请及时联系客服获取专业支持。",
    "6.5666"
}

-- 其他可配置项
ui_config = {
    -- 是否显示刷新按钮
    show_refresh_button = true,
    
    -- 注意事项标题
    notice_title = "注意事项",
    
    -- 二维码相关配置
    qr_config = {
        -- 二维码大小
        size = 150,
        -- 是否显示微信图标
        show_wechat_icon = true,
        -- 微信图标大小
        icon_size = 30
    }
}

-- 功能开关
feature_flags = {
    -- 是否启用二维码识别功能
    enable_qr_recognition = true,
    
    -- 是否启用配置热更新
    enable_hot_reload = true,
    
    -- 是否启用错误上报
    enable_error_reporting = true
}

-- 返回配置给解析器使用
return {
    config_info = config_info,
    notice_items = notice_items,
    ui_config = ui_config,
    feature_flags = feature_flags
}
