import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/xiaoxin_menu_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/function/function_item.dart';
import 'package:tap_debouncer/tap_debouncer.dart';

class FunctionPage extends StatefulWidget {
  const FunctionPage({super.key});

  @override
  State<FunctionPage> createState() => _FunctionPageState();
}

class _FunctionPageState extends State<FunctionPage> {
  List<XiaoxinSchoolModel> menus = [];
  StreamSubscription? _tabSubscription;

  void _getMenuList() {
    UserServiceProvider().getFunctionlMenuList(
      cacheCallBack: (value) {
        _formatMenuListData(value, true);
      },
      successCallBack: (value) {
        _formatMenuListData(value, false);
      },
      errorCallBack: (value) {
        ToastUtils.show(value.msg);
      },
    );
  }

  _formatMenuListData(ResultData? value, bool isCache) {
    menus.clear();
    for (var e in value?.data) {
      List<XiaoxinMenusModel>? models = [];
      for (var e in e['menus'] ?? []) {
        models.add(XiaoxinMenusModel(
            id: e['id'],
            icon: e['icon'],
            jump: e['jump'] ?? [],
            name: e['name'],
            sort: e['sort'],
            style: e['style']));
      }

      models.sort((a, b) => a.sort.compareTo(b.sort));

      XiaoxinSchoolModel schoolModel = XiaoxinSchoolModel(
        id: e['title']['id'],
        name: e['title']['name'],
        sort: e['title']['sort'],
        menus: models,
      );

      menus.add(schoolModel);
      menus.sort((a, b) => a.sort.compareTo(b.sort));
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _subscriptionTabChangeBloc() {
    _tabSubscription?.cancel();
    _tabSubscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 3) {
        print("-- 进入功能页面 ${state.page}");
        _getMenuList();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    print('=============== 进入功能页面');
    _getMenuList();
    _subscriptionTabChangeBloc();
  }

  @override
  void dispose() {
    _tabSubscription?.cancel();
    print('=============== 离开功能页面');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.bottomLeft,
              height: 88.h,
              child: Text('功能',
                  style: TextStyle(
                          color: AppTheme.colorBlackTitle, fontSize: 22.sp)
                      .pfSemiBold),
            ),
            ...menus.map((item) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (item.menus.isNotEmpty) _buildGroupTitle(item.name, 30),
                  if (item.menus.isNotEmpty) SizedBox(height: 15.h),
                  _buildSimpleIconGrid(context, items: [
                    ...item.menus.map((e) {
                      bool isDev = false;
                      bool isOpen = true;
                      Map? sValue;

                      if (e.style != null && e.style!.isNotEmpty) {
                        int index =
                            e.style?.indexWhere((s) => s['key'] == 'flutter') ??
                                -1;
                        if (index != -1) {
                          sValue = jsonDecode(e.style?[index]['value']);
                          if (sValue?.containsKey('isDev') ?? false) {
                            isDev = sValue?['isDev'];
                          } else if (sValue?.containsKey('isOpen') ?? false) {
                            isOpen = sValue?['isOpen'];
                          }
                        }
                      }
                      return TapDebouncer(onTap: () async {
                        if (await UserServiceProvider()
                            .checkFunctionUserPermission(e.id)) {
                          NavigatorPushUtils.to(context, e.jump, e.style, () {
                            _getMenuList();
                          });
                        }
                      }, builder: (_, TapDebouncerFunc? onTap) {
                        return FunctionItem(
                            title: e.name,
                            image: e.icon,
                            isDev: isDev,
                            isOpen: isOpen,
                            onTap: onTap);
                      });
                    }),
                  ]),
                ],
              );
            }),
          ],
        ),
      )),
    );
  }

  Widget _buildGroupTitle(String title, double topPadding) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding, left: 0),
      child: Text(
        title,
        style:
            TextStyle(fontSize: 16.sp, color: AppTheme.colorBlackTitle).phBold,
      ),
    );
  }

  Widget _buildSimpleIconGrid(BuildContext context,
      {required List<Widget> items}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0, left: 0),
      child: Wrap(
        spacing: 14.w,
        runSpacing: 14.w,
        children: items,
      ),
    );
  }
}
