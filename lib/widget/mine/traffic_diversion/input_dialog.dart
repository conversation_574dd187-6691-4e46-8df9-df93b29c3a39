import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class InputDialog extends StatelessWidget {
  final TextEditingController controller;
  final String title;
  final String hintText;
  final String tipStr;
  final TextInputType? keyboardType;
  const InputDialog(
      {super.key,
      required this.controller,
      required this.title,
      required this.hintText,
      required this.tipStr,
      this.keyboardType});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        NavigatorUtils.pop(context);
      },
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
            width: 323.w,
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(20.r)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 17.sp)
                          .pfSemiBold,
                ),
                SizedBox(height: 24.h),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: TextField(
                    enabled: false,
                    keyboardType: keyboardType ?? TextInputType.name,
                    controller: controller,
                    style: TextStyle(
                            color: const Color(0xFF333333), fontSize: 14.sp)
                        .pfMedium,
                    cursorColor: AppTheme.colorBlue,
                    decoration: InputDecoration(
                      hintText: hintText,
                      hintStyle: TextStyle(
                              color: const Color(0xFF606266), fontSize: 14.sp)
                          .pfRegular,
                      border: InputBorder.none,
                    ),
                  ),
                ),
                SizedBox(height: 29.h),
                Row(
                  children: [
                    Expanded(
                        child: GestureDetector(
                      onTap: () => NavigatorUtils.pop(context),
                      child: Container(
                        alignment: Alignment.center,
                        height: 45.h,
                        decoration: BoxDecoration(
                            color: const Color(0xFFECECEC),
                            borderRadius: BorderRadius.circular(13.r)),
                        child: Text(
                          '取消',
                          style: TextStyle(
                                  color: const Color(0xFFB3B3B3),
                                  fontSize: 15.sp)
                              .pfMedium,
                        ),
                      ),
                    )),
                    SizedBox(width: 19.w),
                    Expanded(
                        child: GestureDetector(
                      onTap: () {},
                      child: Container(
                        alignment: Alignment.center,
                        height: 45.h,
                        decoration: BoxDecoration(
                            color: AppTheme.colorBlue,
                            borderRadius: BorderRadius.circular(13.r)),
                        child: Text(
                          '确定',
                          style: TextStyle(color: Colors.white, fontSize: 15.sp)
                              .pfMedium,
                        ),
                      ),
                    )),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
