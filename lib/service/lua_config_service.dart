import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';

/// Lua配置服务
/// 负责从服务端下载Lua配置文件并解析
class LuaConfigService {
  static final LuaConfigService _instance = LuaConfigService._internal();
  factory LuaConfigService() => _instance;
  LuaConfigService._internal();

  final Dio _dio = Dio();

  /// 配置文件缓存路径
  Future<String> get _configCachePath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/lua_configs';
  }

  /// 下载并解析注意事项配置
  Future<List<String>> downloadNoticeConfig({
    String? configUrl,
    bool forceUpdate = false,
  }) async {
    try {
      // 默认配置URL（实际项目中应该从配置中获取）
      final url =
          configUrl ?? 'https://your-server.com/configs/notice_config.lua';

      // 获取缓存文件路径
      final cachePath = await _configCachePath;
      final cacheDir = Directory(cachePath);
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final cacheFile = File('$cachePath/notice_config.lua');

      // 检查是否需要下载
      if (!forceUpdate && await cacheFile.exists()) {
        // 检查缓存文件是否过期（例如：24小时）
        final lastModified = await cacheFile.lastModified();
        final now = DateTime.now();
        if (now.difference(lastModified).inHours < 24) {
          // 使用缓存文件
          final content = await cacheFile.readAsString();
          return _parseLuaConfig(content);
        }
      }

      // 从服务端下载配置文件
      debugPrint('正在下载Lua配置文件: $url');
      final response = await _dio.get(url);

      if (response.statusCode == 200) {
        final content = response.data.toString();

        // 保存到缓存
        await cacheFile.writeAsString(content);

        // 解析并返回
        return _parseLuaConfig(content);
      } else {
        throw Exception('下载配置文件失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('下载Lua配置失败: $e');

      // 尝试使用缓存文件
      final cachePath = await _configCachePath;
      final cacheFile = File('$cachePath/notice_config.lua');
      if (await cacheFile.exists()) {
        final content = await cacheFile.readAsString();
        return _parseLuaConfig(content);
      }

      // 返回默认配置
      return _getDefaultNoticeConfig();
    }
  }

  /// 解析Lua配置文件
  List<String> _parseLuaConfig(String luaContent) {
    try {
      // 简单的Lua数组解析
      // 实际项目中可能需要更复杂的Lua解析器

      // 查找notice_items数组
      final regex = RegExp(r'notice_items\s*=\s*\{([^}]+)\}', multiLine: true);
      final match = regex.firstMatch(luaContent);

      if (match != null) {
        final arrayContent = match.group(1)!;

        // 提取字符串项
        final itemRegex = RegExp(r'"([^"]+)"');
        final items =
            itemRegex.allMatches(arrayContent).map((m) => m.group(1)!).toList();

        if (items.isNotEmpty) {
          return items;
        }
      }

      // 如果解析失败，返回默认配置
      return _getDefaultNoticeConfig();
    } catch (e) {
      debugPrint('解析Lua配置失败: $e');
      return _getDefaultNoticeConfig();
    }
  }

  /// 获取默认注意事项配置
  List<String> _getDefaultNoticeConfig() {
    return [
      '1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。',
      '2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。',
      '3.系统会自动为您的二维码添加微信图标，提升识别度。',
      '4.建议定期更新您的联系方式，确保学员能够及时联系到您。',
      '5.sssss',
    ];
  }

  /// 清除配置缓存
  Future<void> clearCache() async {
    try {
      final cachePath = await _configCachePath;
      final cacheDir = Directory(cachePath);
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('清除缓存失败: $e');
    }
  }

  /// 模拟Lua配置文件内容（用于测试）
  String get mockLuaConfig => '''
-- 注意事项配置
notice_items = {
  "1.当您推广在线产品工具，学员浏览时，以上名片信息会以各种形式展示给学员，学员点击咨询即可展示您的手机号与微信二维码。",
  "2.请确保上传的微信二维码清晰可见，避免影响学员扫码体验。",
  "3.系统会自动为您的二维码添加微信图标，提升识别度。",
  "4.建议定期更新您的联系方式，确保学员能够及时联系到您。",
  "5.如遇到技术问题，请及时联系客服获取帮助。"
}

-- 其他配置项
config_version = "1.0.0"
last_updated = "2024-01-01"
''';
}
