import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/mine/traffic_diversion/input_dialog.dart';

class TrafficDiversionPage extends StatefulWidget {
  const TrafficDiversionPage({super.key});

  @override
  State<TrafficDiversionPage> createState() => _TrafficDiversionPageState();
}

class _TrafficDiversionPageState extends State<TrafficDiversionPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '引流名片'),
      backgroundColor: AppTheme.colorPageBacground,
      body: <PERSON><PERSON><PERSON>ox(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        child: Column(
          children: [
            _videoView(),
            <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
            _inputView(),
          ],
        ),
      ),
    );
  }

  Widget _videoView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 16.w),
      decoration: BoxDecoration(
          color: const Color(0xFFEEF4FF),
          borderRadius: BorderRadius.circular(6.r)),
      child: GestureDetector(
        onTap: () {},
        child: Row(
          children: [
            Image.asset('assets/png/mine/tip_left_img.png',
                width: 24, height: 24),
            SizedBox(width: 6.w),
            Expanded(
              child: Text(
                '点击视频教程快速了解推广名片使用场景',
                style: TextStyle(
                  color: AppTheme.colorBlue,
                  fontSize: 12.sp,
                ).pfMedium,
              ),
            ),
            Text(
              '视频教程',
              style: TextStyle(
                      color: AppTheme.colorBlue,
                      fontSize: 12.sp,
                      decoration: TextDecoration.underline,
                      decorationColor: AppTheme.colorBlue)
                  .pfMedium,
            )
          ],
        ),
      ),
    );
  }

  Widget _inputView() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          _textFieldView("您的昵称", "如：草草老师、蔡文姬等", _nameController, "请输入昵称"),
          SizedBox(height: 30.h),
          _textFieldView("您的手机号", "学员可以联系到您", _phoneController, "请输入手机号",
              keyboardType: TextInputType.phone),
          SizedBox(height: 30.h),
          _wechatView(),
        ],
      ),
    );
  }

  Widget _textFieldView(
    String title,
    String tipStr,
    TextEditingController controller,
    String hintText, {
    TextInputType? keyboardType,
  }) {
    return GestureDetector(
      onTap: () {
        showDialog(
            context: context,
            builder: (_) {
              return InputDialog(
                  controller: controller,
                  title: title,
                  hintText: hintText,
                  tipStr: tipStr);
            });
      },
      child: SizedBox(
        width: ScreenUtil().screenWidth - 32.w,
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  title,
                  style:
                      TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                          .pfMedium,
                ),
                SizedBox(width: 8.w),
                Text(
                  '($tipStr)',
                  style:
                      TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                          .pfRegular,
                )
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      enabled: false,
                      keyboardType: keyboardType ?? TextInputType.name,
                      controller: controller,
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfMedium,
                      cursorColor: AppTheme.colorBlue,
                      decoration: InputDecoration(
                        hintText: hintText,
                        hintStyle: TextStyle(
                                color: const Color(0xFF606266), fontSize: 14.sp)
                            .pfRegular,
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                  Image.asset('assets/png/arrow_right.png',
                      width: 17, height: 17),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _wechatView() {
    return SizedBox(
      width: ScreenUtil().screenWidth - 32.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '您的微信码',
            style: TextStyle(color: const Color(0xFF2D2D2D), fontSize: 15.sp)
                .pfMedium,
          ),
          SizedBox(height: 8.w),
          Text(
            '(可以上传多个，每次随机推荐给学员，长按可删除)',
            style: TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)
                .pfRegular,
          ),
          SizedBox(height: 10.w),
          Expanded(
              child: GridView.builder(
                  gridDelegate: gridDelegate, itemBuilder: itemBuilder))
        ],
      ),
    );
  }
}
