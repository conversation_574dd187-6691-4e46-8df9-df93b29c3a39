import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/mine/me_nodel.dart';
import 'package:npemployee/model/mine/old_person_model.dart';
import 'package:npemployee/model/mine/team_manager_person_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/login/login_page.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/mine/employee_info_section_loggedout.dart';
import '../../provider/user_service_provider.dart';
import '../../widget/mine/employee_info_section.dart';
import '../../widget/mine/profile_option_cell.dart';

class ProfilePage extends StatefulWidget {
  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  bool isregister = false;

  List<DepartmentModel> departmentModels = [];

  Map? reviewer;

  String reviewStatus = '';

  OldPersonModel? oldPersonModel;

  String _promotionDepartName = '';

  int approvalCount = 0;
  int taskCount = 0;

  StreamSubscription? _tabSubscription;

  @override
  void initState() {
    super.initState();

    ///监听页面切换
    _subscriptionTabChangeBloc();

    ///获取用户信息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!AppInfo().registered) {
        _getOldPersonData();
      }
      _checkReviewStatus();
    });
  }

  void _getPromotionDepartName() async {
    int? id = GlobalPreferences().userInfo?.user.promotion_department_id;
    try {
      if (id == null) {
        _promotionDepartName = '未绑定';
      } else {
        ResultData? res = await UserServiceProvider().getDepartInfoWithId(id);
        if (res?.code == 0) {
          _promotionDepartName = res?.data?['name'];
        } else {
          _promotionDepartName = '未绑定';
        }
        setState(() {});
      }
    } catch (e) {
      _promotionDepartName = '未绑定';
      setState(() {});
    }
  }

  void _getOldPersonData() {
    UserServiceProvider().getDistributionSyetemInfo('').then((res) {
      if (res?.code == 0) {
        oldPersonModel = OldPersonModel.fromJson(res?.data);
      }
    });
  }

  void _checkReviewStatus() async {
    UserServiceProvider().getUserInfoWithCache(cacheCallBack: (data) {
      _formatUserInfo(data, true);
    }, successCallBack: (data) {
      _formatUserInfo(data, false);
    }, errorCallBack: (data) {
      ToastUtils.show(data.msg);
    });

    //获取审批及任务待办数量，并通知tab
    ResultData? approvalData =
        await UserServiceProvider().getApprovalList('REVIEWING');
    approvalCount = approvalData?.count ?? 0;
    ResultData? taskData =
        await UserServiceProvider().getMissionListAsync(['missioning']);
    taskCount = taskData?.count ?? 0;
    setState(() {});
    BlocManager().tabBloc.add(TabUnreadEvent(taskCount + approvalCount));
  }

  void _formatUserInfo(ResultData? value, bool isCache) {
    reviewStatus = value?.data['user']['status'];
    if (value?.code == 0) {
      if (value?.data['user']['status'] == "normal") {
        setState(() {
          isregister = true;
        });
        GlobalPreferences().userInfo = MeModel.fromJson(value?.data);
        //格式化部门数据
        _getDepartmentModelsAction();
        _getPromotionDepartName();
      } else if (value?.data['user']['status'] == "waiting_review") {
        UserServiceProvider().getMyReviewer().then((data) {
          if (data?.code == 0) {
            reviewer = data?.data;
            setState(() {});
          }
        });
      }
      setState(() {});
    }
  }

  void _subscriptionTabChangeBloc() {
    _tabSubscription?.cancel();
    _tabSubscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 4) {
        _checkReviewStatus();
      }
    });
  }

  void _getDepartmentModelsAction() {
    departmentModels.clear();
    List<RolesModel> roles = GlobalPreferences().userInfo?.roles ?? [];
    roles.forEach((role) {
      String d = role.department.name;
      int id = role.department.id;
      List<RolesRolesModel> r = role.roles;
      List<String> myRoles = [];
      r.forEach((e) {
        myRoles.add(e.role.name);
      });
      departmentModels
          .add(DepartmentModel(id: id, name: d, positions: myRoles));
    });
    departmentModels.sort((a, b) => a.id.compareTo(b.id));
    if (GlobalPreferences().departmentName == null &&
        departmentModels.isNotEmpty) {
      GlobalPreferences().departmentName = departmentModels.first.name;
    }
  }

  @override
  void dispose() {
    _tabSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Obtain the current screen width
    final double screenWidth = MediaQuery.of(context).size.width;

    // Set the original aspect ratio of your SVG image (width / height)
    const double originalWidth = 375.0;
    const double originalHeight = 150.0;
    final double aspectRatio = originalWidth / originalHeight;

    // Calculate the image height based on the current screen width
    final double calculatedHeight = screenWidth / aspectRatio;

    String workNumber = GlobalPreferences().userInfo?.user.work_no != null &&
            GlobalPreferences().userInfo!.user.work_no!.isNotEmpty
        ? ' | 工号：${GlobalPreferences().userInfo!.user.work_no}'
        : '';

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor, // Light background color
      body: Stack(
        children: [
          // SVG background at the top
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SvgPicture.asset(
              'assets/svg/mine/profile/profile_top_bg.svg',
              fit: BoxFit.cover,
              width: screenWidth,
              height: calculatedHeight, // Set the dynamic height
            ),
          ),
          // Content overlay
          Positioned(
              top: 0, // Adjust this value based on the height of the SVG
              left: 20,
              right: 20,
              child: Container(
                height: 88.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '我的',
                      style: TextStyle(
                              fontSize: 22.sp, color: AppTheme.colorBlackTitle)
                          .pfSemiBold,
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            WeChatService().launchWeChatWork();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(5),
                            child: Image.asset(
                              'assets/png/mine/kefu.png',
                              width: 24, // Adjust size as needed
                              height: 24,
                            ),
                          ),
                        ),
                        SizedBox(width: 20.w - 10),
                        GestureDetector(
                          onTap: () {
                            NavigatorUtils.push(
                                context, MineRouter.personalSettingsPage,
                                arguments: {
                                  'avatar':
                                      '${GlobalPreferences().userInfo?.user.avatar}',
                                }).then((v) {
                              _checkReviewStatus();
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(5),
                            child: SvgPicture.asset(
                              'assets/svg/mine/profile/profile_setting.svg',
                              width: 24, // Adjust size as needed
                              height: 24,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),

          // Scrollable content
          Positioned.fill(
            top: 90,
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20), // Adjust offset space if needed
                  isregister
                      ? (departmentModels.isNotEmpty
                          ? EmployeeInfoSection(
                              userName:
                                  GlobalPreferences().userInfo?.user.name ?? '',
                              promotionDepartName: _promotionDepartName,
                              userId:
                                  '${GlobalPreferences().userInfo?.user.mobile}$workNumber',
                              profileImage:
                                  '${GlobalPreferences().userInfo?.user.avatar}',
                              departments: departmentModels,
                              onDepartmentSelected: (index) {
                                print('Selected department index: $index');
                              },
                            )
                          : Container())
                      : EmployeeInfoSectionLoggedout(
                          onRefreshReviewStatus: () {
                            _checkReviewStatus();
                          },
                          onRegisterTap: () {
                            NavigatorUtils.push(
                                    context, MineRouter.personalInfoPage)
                                .then((v) {
                              _checkReviewStatus();
                            });
                          },
                          onExitTap: () {
                            Navigator.pushAndRemoveUntil(context,
                                MaterialPageRoute(builder: (_) {
                              return LoginPage();
                            }), (route) => false);
                          },
                          reviewStatus: reviewStatus,
                          reviewer: reviewer,
                          avatarPath: null),
                  const SizedBox(height: 16),
                  if (isregister)
                    ProfileOptionCell(
                      title: '引流名片',
                      svgAssetPath: 'assets/svg/mine/profile/yin_liu_card.svg',
                      trailingText: '可在各小程序展出',
                      onTap: () {
                        NavigatorUtils.push(
                                context, MineRouter.trafficDiversionPage)
                            .then((value) {
                          _checkReviewStatus();
                        });
                      },
                    ),
                  const SizedBox(height: 16),
                  if (isregister)
                    ProfileOptionCell(
                      title: '我的审批',
                      svgAssetPath: 'assets/svg/mine/profile/my_approval.svg',
                      trailingText:
                          approvalCount <= 0 ? null : '$approvalCount项待审批',
                      trailingTextColor: const Color(0xFFFE1212),
                      onTap: () {
                        NavigatorUtils.push(context, MineRouter.approvalPage)
                            .then((value) {
                          _checkReviewStatus();
                        });
                      },
                    ),
                  if (isregister) const SizedBox(height: 12),
                  if (isregister)
                    ProfileOptionCell(
                      title: '团队管理',
                      svgAssetPath:
                          'assets/svg/mine/profile/team_management.svg',
                      onTap: () {
                        NavigatorUtils.push(
                          context,
                          MineRouter.teamManagerPage,
                        );
                      },
                    ),
                  if (isregister) const SizedBox(height: 12),
                  if (isregister)
                    ProfileOptionCell(
                      title: '正在学习',
                      svgAssetPath:
                          'assets/svg/mine/profile/currently_studying.svg',
                      onTap: () {
                        NavigatorUtils.push(
                            context, MineRouter.studyingListPage);
                      },
                    ),
                  if (isregister) const SizedBox(height: 12),
                  if (isregister)
                    ProfileOptionCell(
                      title: '我的任务',
                      svgAssetPath: 'assets/svg/mine/profile/my_tasks.svg',
                      trailingText: taskCount <= 0 ? null : '$taskCount项待办',
                      trailingTextColor: const Color(0xFFFE1212),
                      onTap: () {
                        NavigatorUtils.push(context, MineRouter.myTaskPage)
                            .then((c) {
                          _checkReviewStatus();
                        });
                      },
                    ),
                  if (isregister) const SizedBox(height: 12),
                  if (isregister)
                    ProfileOptionCell(
                      title: '我的下载',
                      svgAssetPath: 'assets/svg/mine/profile/my_downloads.svg',
                      onTap: () {
                        // Handle tap event
                        NavigatorUtils.push(context, StudyRouter.myDownload);
                      },
                    ),

                  const SizedBox(height: 12),
                  ProfileOptionCell(
                    title: '关于我们',
                    svgAssetPath: 'assets/svg/mine/profile/about_us.svg',
                    onTap: () {
                      NavigatorUtils.push(context, MineRouter.aboutUsPage);
                    },
                  ),
                  const SizedBox(height: 20), // Add some bottom padding
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
