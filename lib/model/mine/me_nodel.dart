import 'package:npemployee/model/mine/team_manager_person_model.dart';

class MeModel {
  final MeUserModel user;
  final List<RolesModel> roles;

  MeModel(this.user, this.roles);

  factory MeModel.fromJson(Map json) {
    MeUserModel me = MeUserModel.fromJson(json['user']);
    List<RolesModel> meRoles = [];
    List meRolesJson = json['roles'] ?? [];
    for (var element in meRolesJson) {
      meRoles.add(RolesModel.formJson(element));
    }
    return MeModel(me, meRoles);
  }

  Map toJson() {
    return {
      'user': user.toJson(),
      'roles': roles.map((e) => e.toJson()).toList(),
    };
  }

  String get departmentName {
    List<String> departmentNames = [];
    for (var element in roles) {
      departmentNames.add(element.department.name);
    }
    return departmentNames.join(',');
  }

  String get departmentTypeMin {
    //最早加入的部门type
    String result = roles
        .reduce((a, b) => a.department.id < b.department.id ? a : b)
        .department
        .department_type;
    return result;
  }

  String get departmentNameMin {
    //最早加入的部门名称
    String result = roles
        .reduce((a, b) => a.department.id < b.department.id ? a : b)
        .department
        .name;
    return result;
  }

  int get departmentIdMin {
    //最早加入的部门Id
    int result = roles
        .reduce((a, b) => a.department.id < b.department.id ? a : b)
        .department
        .id;
    return result;
  }

  Map get roleNames {
    Map result = {};
    for (var r in roles) {
      result["${r.department.id}"] = r.roles.map((e) => e.role.name).toList();
    }
    return result;
  }

  Map get roleIds {
    Map result = {};
    for (var e in roles) {
      result["${e.department.id}"] = e.roles.map((e) => e.role.id).toList();
    }
    return result;
  }
}

class MeUserModel {
  final String avatar;
  final int? distribution_department_id;
  final String guid;
  final int id;
  final String? id_no;
  final String? last_update_user;
  final String mobile;
  final String name;
  final String? nike_name;
  final String? note;
  final int? promotion_department_id;
  final String? reviewer_guid;
  final String? status;
  final String? work_no;
  final String? created_at;
  final int? guest_identity_id;
  final String? join_at;
  final String? birth;
  final List? wechat_codes;

  MeUserModel(
    this.avatar,
    this.distribution_department_id,
    this.guid,
    this.id,
    this.id_no,
    this.last_update_user,
    this.mobile,
    this.name,
    this.nike_name,
    this.note,
    this.promotion_department_id,
    this.reviewer_guid,
    this.status,
    this.work_no,
    this.created_at,
    this.guest_identity_id,
    this.join_at,
    this.birth,
    this.wechat_codes,
  );

  factory MeUserModel.fromJson(Map json) {
    if (json['join_at'] == '0001-01-01T00:00:00Z') {
      json['join_at'] = null;
    }
    return MeUserModel(
        json['avatar'],
        json['distribution_department_id'],
        json['guid'],
        json['id'],
        json['id_no'],
        json['last_update_user'],
        json['mobile'],
        json['name'],
        json['nike_name'],
        json['note'],
        json['promotion_department_id'],
        json['reviewer_guid'],
        json['status'],
        json['work_no'],
        json['created_at'],
        json['guest_identity_id'],
        json['join_at'],
        json['birth'],
        json['wechat_codes']);
  }

  Map<String, dynamic> toJson() {
    return {
      'avatar': avatar,
      'distribution_department_id': distribution_department_id,
      'guid': guid,
      'id': id,
      'id_no': id_no,
      'last_update_user': last_update_user,
      'mobile': mobile,
      'name': name,
      'nike_name': nike_name,
      'note': note,
      'promotion_department_id': promotion_department_id,
      'reviewer_guid': reviewer_guid,
      'status': status,
      'work_no': work_no,
      'created_at': created_at,
      'guest_identity_id': guest_identity_id,
      'join_at': join_at,
      'birth': birth,
      'wechat_codes': wechat_codes,
    };
  }
}

class ImLoginModel {
  final int expired;
  final String sig;
  final String uid;

  ImLoginModel(this.expired, this.sig, this.uid);

  factory ImLoginModel.fromJson(Map json) {
    return ImLoginModel(json['expired'], json['sig'], json['uid']);
  }

  Map toJson() {
    return {
      'expired': expired,
      'sig': sig,
      'uid': uid,
    };
  }
}
