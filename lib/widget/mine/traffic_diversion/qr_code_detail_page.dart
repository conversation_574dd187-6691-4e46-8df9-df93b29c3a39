import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:qr_flutter/qr_flutter.dart';

/// 二维码详情页面
/// 支持Hero动画、左右滑动切换、长按删除
class QRCodeDetailPage extends StatefulWidget {
  final List<String> qrCodeDataList;
  final int initialIndex;
  final Function(int index)? onDelete;

  const QRCodeDetailPage({
    super.key,
    required this.qrCodeDataList,
    required this.initialIndex,
    this.onDelete,
  });

  @override
  State<QRCodeDetailPage> createState() => _QRCodeDetailPageState();
}

class _QRCodeDetailPageState extends State<QRCodeDetailPage> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Container(),
        leading: Container(),
      ),
      body: GestureDetector(
        onTap: () {
          // 点击空白区域返回
          Navigator.of(context).pop();
        },
        child: Stack(
          children: [
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.qrCodeDataList.length,
              itemBuilder: (context, index) {
                return _buildQRCodePage(index);
              },
            ),
            // 页面指示器
            if (widget.qrCodeDataList.length > 1)
              Positioned(
                bottom: 50.h,
                left: 0,
                right: 0,
                child: _buildPageIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  // 构建单个二维码页面
  Widget _buildQRCodePage(int index) {
    return SingleChildScrollView(
      child: SizedBox(
        height: ScreenUtil().screenHeight - 100.h, // 减去AppBar和底部安全区域
        child: Center(
          child: Hero(
            tag: 'qr_code_$index',
            child: GestureDetector(
              onTap: () {
                // 阻止点击二维码时触发返回
              },
              onLongPress: _showDeleteDialog,
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
                padding: EdgeInsets.all(20.w),
                constraints: BoxConstraints(
                  maxHeight: ScreenUtil().screenHeight * 0.7, // 最大高度限制
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                  boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10)),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '微信二维码',
                      style: TextStyle(
                        color: const Color(0xFF2D2D2D),
                        fontSize: 18.sp,
                      ).pfMedium,
                    ),
                    SizedBox(height: 15.h),
                    Flexible(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // 二维码
                          QrImageView(
                            data: widget.qrCodeDataList[index],
                            size: 220.w, // 稍微减小尺寸
                            backgroundColor: Colors.white,
                          ),
                          // 中心的微信图标
                          Container(
                            width: 35.w,
                            height: 35.w,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            child: Image.asset(
                              'assets/png/im/wechat_icon.png',
                              width: 28.w,
                              height: 28.w,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.wechat,
                                  size: 28.w,
                                  color: Colors.black,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 15.h),
                    Text(
                      '长按可删除 · 点击空白区域返回',
                      style: TextStyle(
                        color: const Color(0xFF999999),
                        fontSize: 12.sp,
                      ).pfRegular,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 构建页面指示器
  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        widget.qrCodeDataList.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: 8.w,
          height: 8.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: index == _currentIndex
                ? Colors.white
                : Colors.white.withOpacity(0.4),
          ),
        ),
      ),
    );
  }

  // 显示删除确认对话框
  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('删除二维码'),
          content: const Text('确定要删除这个二维码吗？'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteCurrentQRCode();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 删除当前二维码
  void _deleteCurrentQRCode() {
    if (widget.qrCodeDataList.length <= 1) {
      // 如果只有一个二维码，删除后返回上一页
      widget.onDelete?.call(_currentIndex);
      Navigator.of(context).pop();
    } else {
      // 如果有多个二维码，删除当前的并调整索引
      widget.onDelete?.call(_currentIndex);

      // 调整当前索引
      if (_currentIndex >= widget.qrCodeDataList.length) {
        _currentIndex = widget.qrCodeDataList.length - 1;
      }

      // 更新PageView
      if (_currentIndex >= 0) {
        _pageController.animateToPage(
          _currentIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }

      setState(() {});
    }
  }
}
